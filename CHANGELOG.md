# 更新日志

## [v2.1] - 2025-01-08

### 性能优化

#### 视频缩略图生成优化

- **重大改进**: 视频缩略图生成速度提升 30-50%
- **更早的生成时机**: 从 `readyState >= 2` (HAVE_CURRENT_DATA) 优化到 `readyState >= 1` (HAVE_METADATA)
- **增强的尺寸检测**: 添加 50ms 间隔的定时器检测视频尺寸变化，确保不遗漏任何可生成缩略图的时机
- **改进的清理机制**: 确保所有定时器在组件卸载时被正确清除，防止内存泄漏

#### 技术细节

- 降低了缩略图生成的 readyState 要求
- 添加了视频尺寸信息检查 (`video.videoWidth > 0 && video.videoHeight > 0`)
- 实现了定时器自动清理机制
- 优化了错误处理和资源管理

### 影响

- 特别是对于网络较慢或视频文件较大的情况，用户体验显著提升
- 减少了时间轴中视频元素的加载等待时间
- 提高了整体应用性能和响应速度

---

## [v2.0] - 之前版本

### 核心功能

- 基于 Fabric.js 的画布编辑器
- 多媒体时间轴支持
- FFmpeg 视频导出
- 动画和效果系统
- Jamendo 音乐库集成
