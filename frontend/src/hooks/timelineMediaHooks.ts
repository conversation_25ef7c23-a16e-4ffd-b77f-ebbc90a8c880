import React, { useEffect, useRef } from "react";
import { useWavesurferWaveform } from "./useWavesurferWaveform";

/**
 * 处理GIF静态缩略图的Hook
 * @param element GIF元素
 * @returns 包含缩略图URL和加载状态的对象
 */
export const useGifStaticThumbnail = (element: any) => {
  const [gifStaticThumbnail, setGifStaticThumbnail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  useEffect(() => {
    if (element.type !== "gif" || !element.properties?.src) {
      setGifStaticThumbnail("");
      setIsLoading(false);
      setHasError(false);
      return;
    }

    // 重置状态
    setGifStaticThumbnail("");
    setIsLoading(true);
    setHasError(false);

    // 创建一个临时的img元素来加载GIF
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = element.properties.src;

    img.onload = () => {
      // 创建canvas来绘制GIF的第一帧
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      // 绘制图片（这会自动显示第一帧）
      ctx.drawImage(img, 0, 0);

      // 转换为静态图片URL
      setGifStaticThumbnail(canvas.toDataURL("image/jpeg", 0.8));
      setIsLoading(false);
    };

    img.onerror = (err) => {
      console.error("Error loading GIF for static thumbnail:", err);
      setGifStaticThumbnail("");
      setIsLoading(false);
      setHasError(true);
    };

    // 清理函数
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [element.type, element.properties?.src]);

  return {
    gifStaticThumbnail,
    isLoading,
    hasError,
  };
};

/**
 * 稳定的视频缩略图生成器类
 * 基于 thumbnail.jsx 的稳定性设计
 */
class StableVideoThumbnailGenerator {
  private video: HTMLVideoElement;
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D | null;
  private isDestroyed = false;
  private retryCount = 0;
  private maxRetries = 2;

  constructor(
    private src: string,
    private onProgress: (progress: number) => void,
    private onSuccess: (thumbnailUrl: string) => void,
    private onError: (error: Error) => void
  ) {
    this.video = document.createElement("video");
    this.canvas = document.createElement("canvas");
    this.ctx = this.canvas.getContext("2d");
    this.setupVideo();
  }

  private setupVideo() {
    // 基于 thumbnail.jsx 的稳定配置
    this.video.preload = "metadata";
    this.video.muted = true;
    this.video.playsInline = true;

    // 先尝试使用 crossOrigin，如果失败则重试不使用
    if (this.retryCount === 0) {
      this.video.crossOrigin = "anonymous";
    }

    this.bindEvents();
    this.video.src = this.src;
  }

  private bindEvents() {
    this.video.onloadstart = () => {
      if (this.isDestroyed) return;
      this.onProgress(10);
    };

    this.video.onloadedmetadata = () => {
      if (this.isDestroyed) return;
      this.onProgress(30);

      // 设置画布尺寸，基于 thumbnail.jsx 的逻辑
      this.canvas.width = this.video.videoWidth || 320;
      this.canvas.height = this.video.videoHeight || 240;

      // 定位到第一帧
      this.video.currentTime = 0;
    };

    this.video.onseeked = () => {
      if (this.isDestroyed) return;
      this.onProgress(80);
      this.generateThumbnail();
    };

    this.video.onerror = (event) => {
      if (this.isDestroyed) return;
      this.handleVideoError(event);
    };

    this.video.oncanplay = () => {
      if (this.isDestroyed) return;
      this.onProgress(60);
    };
  }

  private generateThumbnail() {
    try {
      if (!this.ctx || this.isDestroyed) {
        throw new Error("Canvas context not available");
      }

      // 基于 thumbnail.jsx 的绘制逻辑
      this.ctx.drawImage(
        this.video,
        0,
        0,
        this.canvas.width,
        this.canvas.height
      );

      // 生成高质量缩略图
      const thumbnailUrl = this.canvas.toDataURL("image/jpeg", 0.85);
      this.onProgress(100);
      this.onSuccess(thumbnailUrl);
    } catch (error) {
      console.error("Error generating thumbnail:", error);
      this.handleThumbnailError(error as Error);
    }
  }

  private handleVideoError(event: Event | string) {
    console.error("Video loading error:", event);

    // 如果是第一次尝试且可能是CORS问题，重试不使用crossOrigin
    if (this.retryCount === 0) {
      console.log("Retrying without crossOrigin...");
      this.retryCount++;
      this.cleanup();
      this.setupVideo();
      return;
    }

    // 如果重试次数未达到上限，再次尝试
    if (this.retryCount < this.maxRetries) {
      console.log(`Retrying... (${this.retryCount + 1}/${this.maxRetries})`);
      this.retryCount++;
      setTimeout(() => {
        if (!this.isDestroyed) {
          this.cleanup();
          this.setupVideo();
        }
      }, 1000);
      return;
    }

    this.onError(new Error("Failed to load video after multiple retries"));
  }

  private handleThumbnailError(error: Error) {
    // 如果是安全错误且还有重试机会，尝试不使用crossOrigin
    if (error.name === "SecurityError" && this.retryCount < this.maxRetries) {
      console.log("Security error, retrying without crossOrigin...");
      this.retryCount++;
      this.cleanup();
      this.setupVideo();
      return;
    }

    this.onError(error);
  }

  private cleanup() {
    if (this.video) {
      this.video.onloadstart = null;
      this.video.onloadedmetadata = null;
      this.video.onseeked = null;
      this.video.onerror = null;
      this.video.oncanplay = null;
      this.video.src = "";
    }
  }

  public destroy() {
    this.isDestroyed = true;
    this.cleanup();
  }
}

/**
 * 处理视频缩略图的Hook (改进版)
 * 结合 thumbnail.jsx 的稳定性
 * @param element 视频元素
 * @returns 包含缩略图URL和加载状态的对象
 */
export const useVideoThumbnail = (element: any) => {
  const [videoThumbnail, setVideoThumbnail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [loadingProgress, setLoadingProgress] = React.useState(0);
  const [hasError, setHasError] = React.useState(false);
  const generatorRef = useRef<StableVideoThumbnailGenerator | null>(null);
  const cacheRef = useRef(new Map<string, string>());

  useEffect(() => {
    if (
      element.type !== "video" ||
      !element.properties?.src ||
      element.properties.src.trim() === ""
    ) {
      setVideoThumbnail("");
      setIsLoading(false);
      setLoadingProgress(0);
      setHasError(false);
      return;
    }

    // 检查DOM元素是否存在，如果不存在说明元素已被删除
    if (element.properties.elementId) {
      const domElement = document.getElementById(element.properties.elementId);
      if (!domElement) {
        console.log(
          "Video DOM element not found, element may have been deleted"
        );
        setVideoThumbnail("");
        setIsLoading(false);
        setLoadingProgress(0);
        setHasError(false);
        return;
      }
    }

    const videoSrc = element.properties.src;

    // 检查缓存
    if (cacheRef.current.has(videoSrc)) {
      const cachedThumbnail = cacheRef.current.get(videoSrc)!;
      setVideoThumbnail(cachedThumbnail);
      setIsLoading(false);
      setLoadingProgress(100);
      setHasError(false);
      return;
    }

    // 重置状态
    setVideoThumbnail("");
    setIsLoading(true);
    setLoadingProgress(0);
    setHasError(false);

    // 清理之前的生成器
    if (generatorRef.current) {
      generatorRef.current.destroy();
    }

    // 创建新的稳定缩略图生成器
    generatorRef.current = new StableVideoThumbnailGenerator(
      videoSrc,
      (progress) => {
        if (!generatorRef.current) return;
        setLoadingProgress(progress);
      },
      (thumbnailUrl) => {
        if (!generatorRef.current) return;
        // 缓存结果
        cacheRef.current.set(videoSrc, thumbnailUrl);
        setVideoThumbnail(thumbnailUrl);
        setIsLoading(false);
        setLoadingProgress(100);
        setHasError(false);
      },
      (error) => {
        if (!generatorRef.current) return;
        console.error("Stable video thumbnail generation failed:", error);
        setIsLoading(false);
        setHasError(true);
        setLoadingProgress(0);
      }
    );

    // 清理函数
    return () => {
      if (generatorRef.current) {
        generatorRef.current.destroy();
        generatorRef.current = null;
      }
    };
  }, [element.type, element.properties?.src]);

  return {
    videoThumbnail,
    isLoading,
    loadingProgress,
    hasError,
  };
};

/**
 * 处理音频波形的Hook (使用 Wavesurfer.js)
 * @param element 音频元素
 * @returns 音频波形图URL
 */
export const useAudioWaveform = (element: any) => {
  const [audioWaveform, setAudioWaveform] = React.useState("");
  const [isInitialized, setIsInitialized] = React.useState(false);

  // 只对音频元素处理波形
  const isAudioElement = element.type === "audio";
  const audioSrc = isAudioElement ? element.properties?.src : "";

  // 使用 Wavesurfer.js Hook - 只在是音频元素时才传入有效的src
  const { containerRef, waveformDataUrl, isLoading } = useWavesurferWaveform(
    audioSrc,
    element.timeFrame,
    {
      width: Math.max(
        200,
        ((element.timeFrame.end - element.timeFrame.start) / 1000) * 50
      ), // 根据时长调整宽度
      height: 60,
      waveColor: "#4A90E2",
      progressColor: "#2E5BBA",
      backgroundColor: "#3f51b5",
      barWidth: 1,
      barGap: 1,
      normalize: true,
    }
  );

  // 当 Wavesurfer.js 生成的波形可用时，更新状态
  React.useEffect(() => {
    if (isAudioElement && waveformDataUrl) {
      setAudioWaveform(waveformDataUrl);
      setIsInitialized(true);
    } else if (!isAudioElement) {
      // 对于非音频元素，直接设置为初始化状态
      setAudioWaveform("");
      setIsInitialized(true);
    }
  }, [isAudioElement, waveformDataUrl]);

  // 防止在初始化前显示空白，造成闪烁
  React.useEffect(() => {
    if (isAudioElement && !isLoading && !waveformDataUrl && !isInitialized) {
      // 如果不在加载中且没有波形数据，设置为初始化状态
      setIsInitialized(true);
    }
  }, [isAudioElement, isLoading, waveformDataUrl, isInitialized]);

  // 创建隐藏的容器用于 Wavesurfer.js 渲染
  React.useEffect(() => {
    if (containerRef.current) {
      // 确保容器是隐藏的，只用于生成波形数据
      containerRef.current.style.position = "absolute";
      containerRef.current.style.left = "-9999px";
      containerRef.current.style.top = "-9999px";
      containerRef.current.style.visibility = "hidden";
      containerRef.current.style.pointerEvents = "none";
    }
  }, [containerRef]);

  // 添加隐藏的容器到 DOM 中用于 Wavesurfer.js
  React.useEffect(() => {
    if (!document.getElementById("wavesurfer-container")) {
      const container = document.createElement("div");
      container.id = "wavesurfer-container";
      container.style.position = "absolute";
      container.style.left = "-9999px";
      container.style.top = "-9999px";
      container.style.visibility = "hidden";
      container.style.pointerEvents = "none";
      document.body.appendChild(container);
    }
  }, []);

  // 返回波形数据URL，防止闪烁
  return isInitialized ? audioWaveform : "";
};
