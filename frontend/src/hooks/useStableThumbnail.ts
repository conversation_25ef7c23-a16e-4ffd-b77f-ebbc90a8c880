import React, { useEffect, useRef, useState, useCallback } from "react";

/**
 * 基于 thumbnail.jsx 稳定性的缩略图Hook使用示例
 * 展示如何在时间线组件中集成改进的视频缩略图功能
 */

// 缩略图配置接口
interface ThumbnailConfig {
  width?: number;
  height?: number;
  quality?: number;
  frameCount?: number;
  enableCache?: boolean;
}

// 缩略图结果接口
interface ThumbnailResult {
  thumbnails: string[];
  isLoading: boolean;
  progress: number;
  hasError: boolean;
  retry: () => void;
}

/**
 * 稳定的视频缩略图Hook
 * 结合 thumbnail.jsx 的稳定性和多帧支持
 */
export const useStableThumbnail = (
  videoSrc: string,
  config: ThumbnailConfig = {}
): ThumbnailResult => {
  const {
    width = 160,
    height = 90,
    quality = 0.85,
    frameCount = 1,
    enableCache = true,
  } = config;

  const [thumbnails, setThumbnails] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [hasError, setHasError] = useState(false);
  
  const cacheRef = useRef(new Map<string, string[]>());
  const generatorRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  // 生成缓存键
  const getCacheKey = useCallback(() => {
    return `${videoSrc}-${width}x${height}-${frameCount}-${quality}`;
  }, [videoSrc, width, height, frameCount, quality]);

  // 清理资源
  const cleanup = useCallback(() => {
    if (generatorRef.current) {
      generatorRef.current.onloadedmetadata = null;
      generatorRef.current.onseeked = null;
      generatorRef.current.onerror = null;
      generatorRef.current.src = "";
      generatorRef.current = null;
    }
  }, []);

  // 生成单帧缩略图
  const generateSingleFrame = useCallback(
    (video: HTMLVideoElement, canvas: HTMLCanvasElement, timePoint: number = 0): Promise<string> => {
      return new Promise((resolve, reject) => {
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          reject(new Error("Canvas context not available"));
          return;
        }

        const handleSeeked = () => {
          try {
            ctx.drawImage(video, 0, 0, width, height);
            const thumbnailUrl = canvas.toDataURL("image/jpeg", quality);
            video.removeEventListener("seeked", handleSeeked);
            resolve(thumbnailUrl);
          } catch (error) {
            video.removeEventListener("seeked", handleSeeked);
            reject(error);
          }
        };

        video.addEventListener("seeked", handleSeeked);
        video.currentTime = timePoint;
      });
    },
    [width, height, quality]
  );

  // 生成多帧缩略图
  const generateThumbnails = useCallback(async () => {
    if (!videoSrc) return;

    const cacheKey = getCacheKey();
    
    // 检查缓存
    if (enableCache && cacheRef.current.has(cacheKey)) {
      const cached = cacheRef.current.get(cacheKey)!;
      setThumbnails(cached);
      setProgress(100);
      return;
    }

    setIsLoading(true);
    setProgress(0);
    setHasError(false);
    setThumbnails([]);

    try {
      // 创建视频和画布元素
      const video = document.createElement("video");
      const canvas = document.createElement("canvas");
      
      // 基于 thumbnail.jsx 的稳定配置
      video.preload = "metadata";
      video.muted = true;
      video.playsInline = true;
      video.crossOrigin = "anonymous";
      
      canvas.width = width;
      canvas.height = height;
      
      generatorRef.current = video;
      canvasRef.current = canvas;

      // 等待视频元数据加载
      await new Promise<void>((resolve, reject) => {
        video.onloadedmetadata = () => {
          setProgress(20);
          resolve();
        };
        
        video.onerror = () => {
          // 如果crossOrigin失败，尝试不使用crossOrigin
          if (video.crossOrigin) {
            console.log("Retrying without crossOrigin...");
            video.crossOrigin = "";
            video.src = videoSrc;
          } else {
            reject(new Error("Video loading failed"));
          }
        };
        
        video.src = videoSrc;
      });

      const duration = video.duration;
      const results: string[] = [];

      // 生成多帧缩略图
      for (let i = 0; i < frameCount; i++) {
        const timePoint = frameCount === 1 ? 0 : (i / (frameCount - 1)) * duration;
        const clampedTime = Math.min(timePoint, duration - 0.1);
        
        const thumbnail = await generateSingleFrame(video, canvas, clampedTime);
        results.push(thumbnail);
        
        // 更新进度
        const currentProgress = 20 + ((i + 1) / frameCount) * 80;
        setProgress(currentProgress);
      }

      // 缓存结果
      if (enableCache) {
        cacheRef.current.set(cacheKey, results);
      }

      setThumbnails(results);
      setProgress(100);
    } catch (error) {
      console.error("Thumbnail generation failed:", error);
      setHasError(true);
    } finally {
      setIsLoading(false);
      cleanup();
    }
  }, [videoSrc, getCacheKey, enableCache, frameCount, generateSingleFrame, cleanup]);

  // 重试函数
  const retry = useCallback(() => {
    if (!isLoading) {
      generateThumbnails();
    }
  }, [isLoading, generateThumbnails]);

  // 当依赖变化时重新生成缩略图
  useEffect(() => {
    if (videoSrc) {
      generateThumbnails();
    } else {
      setThumbnails([]);
      setProgress(0);
      setHasError(false);
    }

    // 清理函数
    return cleanup;
  }, [videoSrc, generateThumbnails, cleanup]);

  return {
    thumbnails,
    isLoading,
    progress,
    hasError,
    retry,
  };
};

/**
 * 时间线视频元素缩略图Hook
 * 专门为时间线组件优化的版本
 */
export const useTimelineVideoThumbnail = (element: any) => {
  // 根据元素宽度动态计算帧数
  const calculateFrameCount = useCallback((elementWidth: number) => {
    if (elementWidth < 100) return 1;
    if (elementWidth < 200) return 2;
    if (elementWidth < 400) return 3;
    return Math.min(Math.floor(elementWidth / 100), 8);
  }, []);

  const elementWidth = element.timeFrame ? 
    ((element.timeFrame.end - element.timeFrame.start) / 1000) * 50 : 100;
  
  const frameCount = calculateFrameCount(elementWidth);

  return useStableThumbnail(element.properties?.src || "", {
    width: 30,
    height: 30,
    frameCount,
    quality: 0.7,
    enableCache: true,
  });
};

/**
 * 使用示例：
 * 
 * // 在时间线组件中使用
 * const VideoTimelineElement = ({ element }) => {
 *   const { thumbnails, isLoading, hasError } = useTimelineVideoThumbnail(element);
 *   
 *   const backgroundStyle = useMemo(() => {
 *     if (thumbnails.length > 0) {
 *       return {
 *         backgroundImage: thumbnails.map(url => `url(${url})`).join(', '),
 *         backgroundSize: `${100 / thumbnails.length}% 100%`,
 *         backgroundRepeat: 'no-repeat',
 *         backgroundPosition: thumbnails.map((_, i) => 
 *           `${(i * 100) / (thumbnails.length - 1)}% center`
 *         ).join(', ')
 *       };
 *     }
 *     return {};
 *   }, [thumbnails]);
 *   
 *   return (
 *     <div style={backgroundStyle}>
 *       {isLoading && <LoadingIndicator />}
 *       {hasError && <ErrorIndicator />}
 *     </div>
 *   );
 * };
 */
