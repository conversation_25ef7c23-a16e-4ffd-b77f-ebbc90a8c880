import { useEffect, useRef, useState, useCallback } from "react";

/**
 * 基于 thumbnail.jsx 稳定性的缩略图Hook
 * 生成视频首帧缩略图
 */

// 缩略图配置接口
interface ThumbnailConfig {
  width?: number;
  height?: number;
  quality?: number;
  enableCache?: boolean;
}

// 缩略图结果接口
interface ThumbnailResult {
  thumbnail: string;
  isLoading: boolean;
  progress: number;
  hasError: boolean;
  retry: () => void;
}

/**
 * 稳定的视频缩略图Hook
 * 结合 thumbnail.jsx 的稳定性，生成首帧缩略图
 */
export const useStableThumbnail = (
  videoSrc: string,
  config: ThumbnailConfig = {}
): ThumbnailResult => {
  const {
    width = 160,
    height = 90,
    quality = 0.85,
    enableCache = true,
  } = config;

  const [thumbnail, setThumbnail] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [hasError, setHasError] = useState(false);

  const cacheRef = useRef(new Map<string, string>());
  const generatorRef = useRef<HTMLVideoElement | null>(null);

  // 生成缓存键
  const getCacheKey = useCallback(() => {
    return `${videoSrc}-${width}x${height}-${quality}`;
  }, [videoSrc, width, height, quality]);

  // 清理资源
  const cleanup = useCallback(() => {
    if (generatorRef.current) {
      generatorRef.current.onloadedmetadata = null;
      generatorRef.current.onseeked = null;
      generatorRef.current.onerror = null;
      generatorRef.current.src = "";
      generatorRef.current = null;
    }
  }, []);

  // 生成首帧缩略图
  const generateThumbnail = useCallback(async () => {
    if (!videoSrc) return;

    const cacheKey = getCacheKey();

    // 检查缓存
    if (enableCache && cacheRef.current.has(cacheKey)) {
      const cached = cacheRef.current.get(cacheKey)!;
      setThumbnail(cached);
      setProgress(100);
      return;
    }

    setIsLoading(true);
    setProgress(0);
    setHasError(false);
    setThumbnail("");

    try {
      // 创建视频和画布元素
      const video = document.createElement("video");
      const canvas = document.createElement("canvas");

      // 基于 thumbnail.jsx 的稳定配置
      video.preload = "metadata";
      video.muted = true;
      video.playsInline = true;
      video.crossOrigin = "anonymous";

      canvas.width = width;
      canvas.height = height;

      generatorRef.current = video;

      // 等待视频元数据加载
      await new Promise<void>((resolve, reject) => {
        video.onloadedmetadata = () => {
          setProgress(30);
          // 定位到第一帧
          video.currentTime = 0;
        };

        video.onseeked = () => {
          setProgress(80);
          resolve();
        };

        video.onerror = () => {
          // 如果crossOrigin失败，尝试不使用crossOrigin
          if (video.crossOrigin) {
            console.log("Retrying without crossOrigin...");
            video.crossOrigin = "";
            video.src = videoSrc;
          } else {
            reject(new Error("Video loading failed"));
          }
        };

        video.src = videoSrc;
      });

      // 生成缩略图
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        throw new Error("Canvas context not available");
      }

      ctx.drawImage(video, 0, 0, width, height);
      const thumbnailUrl = canvas.toDataURL("image/jpeg", quality);

      // 缓存结果
      if (enableCache) {
        cacheRef.current.set(cacheKey, thumbnailUrl);
      }

      setThumbnail(thumbnailUrl);
      setProgress(100);
    } catch (error) {
      console.error("Thumbnail generation failed:", error);
      setHasError(true);
    } finally {
      setIsLoading(false);
      cleanup();
    }
  }, [videoSrc, getCacheKey, enableCache, width, height, quality, cleanup]);

  // 重试函数
  const retry = useCallback(() => {
    if (!isLoading) {
      generateThumbnail();
    }
  }, [isLoading, generateThumbnail]);

  // 当依赖变化时重新生成缩略图
  useEffect(() => {
    if (videoSrc) {
      generateThumbnail();
    } else {
      setThumbnail("");
      setProgress(0);
      setHasError(false);
    }

    // 清理函数
    return cleanup;
  }, [videoSrc, generateThumbnail, cleanup]);

  return {
    thumbnail,
    isLoading,
    progress,
    hasError,
    retry,
  };
};

/**
 * 时间线视频元素缩略图Hook
 * 专门为时间线组件优化的版本
 */
export const useTimelineVideoThumbnail = (element: any) => {
  return useStableThumbnail(element.properties?.src || "", {
    width: 30,
    height: 30,
    quality: 0.7,
    enableCache: true,
  });
};
